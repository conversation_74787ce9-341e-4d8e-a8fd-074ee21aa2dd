import { type ColumnDef } from "@tanstack/react-table";
import { useParams } from "next/navigation";
import { useCallback, useEffect, useMemo, useState } from "react";
import _ from "lodash";
import { toast } from "sonner";

import { AccountPlanTableType } from "@/features/account-plan/types";
import {
  APSvot,
  APSvotType,
} from "@/features/account-plan/types/position-types";
import DataTable, { DataTableMeta } from "@/components/ui/data-table";
import { useSvotList } from "@/features/account-plan/api/position-apis/svot/get-svot-list";
import { useUpdateSvot } from "@/features/account-plan/api/position-apis/svot/update-svot";
import { TiptapCell } from "@/components/ui/data-table/data-table-components";

import { AccountTable, AccountTableTitle } from "../base-table";
import { getAccountPlanTableName } from "@/features/account-plan/constants";

const svotOptions = {
  [APSvotType.STRENGTH]: {
    order: 1,
    name: "Strengths",
    description:
      "What strengths do we bring to the situation to help them address an issue and help us advance their perception of us and/or revenue growth.",
  },
  [APSvotType.VULNERABILITY]: {
    order: 2,
    name: "Vulnerabilities",
    description:
      "What are the internal vulnerabilities that exist for us that could impair or jeopardise our ability to advance their perception of us and/or revenue growth.",
  },
  [APSvotType.OPPORTUNITY]: {
    order: 3,
    name: "Opportunities",
    description:
      "What opportunities exist for us to advance their perception of us and/or revenue growth.",
  },
  [APSvotType.THREAT]: {
    order: 4,
    name: "Threats",
    description:
      "What are the external threats that exist for us that could impair or jeopardise our ability to advance their perception of us and/or revenue growth.",
  },
} as const;

export const SvotTable = () => {
  const [tableData, setTableData] = useState<APSvot[]>([]);

  const { id } = useParams<{ id: string }>();
  const accountId = parseInt(id);

  const { svotList } = useSvotList({
    accountId,
    params: {
      disable_pagination: true,
    },
  });
  const updateSvot = useUpdateSvot({});

  useEffect(() => {
    if (!svotList) return;

    const newTableData = svotList
      ?.map((v, idx) => ({
        idx,
        ...v,
      }))
      .sort(
        (a, b) =>
          svotOptions[a.item_type].order - svotOptions[b.item_type].order
      );

    setTableData(newTableData);
  }, [svotList]);

  const onChangeData = useCallback(
    async (data: Partial<APSvot>, id: number) => {
      try {
        setTableData((prev) =>
          prev.map((v) => (v.id === id ? { ...v, ...data } : v))
        );

        await updateSvot.mutateAsync({
          accountId,
          id,
          data,
        });
      } catch (_) {
        toast("An unexpected error occured when modifying data");
      }
    },
    [accountId, updateSvot]
  );

  const columns: ColumnDef<APSvot>[] = useMemo(
    () => [
      {
        accessorKey: "description",
        header: "",
        cell: ({ row, table }) => {
          const isPreview =
            (table.options.meta as DataTableMeta)?.isPreview ?? false;
          const rowData = row.original;
          const itemType = svotOptions[rowData.item_type];

          return (
            <div className={isPreview ? "text-4xl" : "text-[16px]"}>
              <div className="flex items-center bg-neutral-200 px-res-x-base py-res-y-base font-bold">
                {itemType.name}
              </div>
              <TiptapCell
                className={`min-h-[20vh] ${isPreview ? "text-4xl" : "text-[16px]"}`}
                value={rowData.description}
                onChange={(description) =>
                  onChangeData({ description }, rowData.id)
                }
                placeholder={itemType.description}
              />
            </div>
          );
        },
      },
    ],
    [onChangeData]
  );

  return (
    <AccountTable type={AccountPlanTableType.SVOT}>
      <DataTable
        columns={columns}
        data={tableData}
        showHeader={false}
        tableType={AccountPlanTableType.SVOT}
      />
    </AccountTable>
  );
};
