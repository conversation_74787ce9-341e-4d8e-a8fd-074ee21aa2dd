import { type ColumnDef } from "@tanstack/react-table";
import { useParams } from "next/navigation";
import { useCallback, useEffect, useMemo, useState } from "react";
import _ from "lodash";
import { toast } from "sonner";

import {
  AccountPlanData,
  AccountPlanTableType,
} from "@/features/account-plan/types";
import DataTable, { DataTableMeta } from "@/components/ui/data-table";
import {
  EditableNumberCell,
  SelectCell,
} from "@/components/ui/data-table/data-table-components";
import { APRevenueForecast } from "@/features/account-plan/types/revenue-types";

import { AccountTable, AccountTableTitle } from "../base-table";
import { Button } from "@/components/ui/button";

import { useCreateRevenueForecast } from "@/features/account-plan/api/revenue-apis/revenue-forecast/create-revenue-forecast";
import { useUpdateRevenueForecast } from "@/features/account-plan/api/revenue-apis/revenue-forecast/update-revenue-forecast";
import { useDeleteRevenueForecast } from "@/features/account-plan/api/revenue-apis/revenue-forecast/delete-revenue-forecast";
import { useRevenueForecastList } from "@/features/account-plan/api/revenue-apis/revenue-forecast/get-revenue-forecast-list";
import { getAccountPlanTableName } from "@/features/account-plan/constants";

export const RevenueForecastTable = ({
  accountPlan,
}: {
  accountPlan?: AccountPlanData;
}) => {
  const [tableData, setTableData] = useState<APRevenueForecast[]>([]);
  const [rowSelection, setRowSelection] = useState<Record<string, boolean>>({});

  const { id } = useParams<{ id: string }>();
  const accountId = parseInt(id);

  const { revenueForecastList } = useRevenueForecastList({
    accountId,
    params: {
      disable_pagination: true,
    },
  });
  const createRevenueForecast = useCreateRevenueForecast({});
  const updateRevenueForecast = useUpdateRevenueForecast({});
  const deleteRevenueForecast = useDeleteRevenueForecast({});

  useEffect(() => {
    if (!revenueForecastList) return;

    const newTableData = revenueForecastList?.map((v, idx) => ({
      idx,
      ...v,
    }));

    setTableData(newTableData);
  }, [revenueForecastList]);

  const selectedRows = Object.keys(rowSelection)
    .filter((rowId) => rowSelection[rowId])
    .map((idx) => tableData[parseInt(idx)]);

  const onAddRow = async () => {
    try {
      const res = await createRevenueForecast.mutateAsync({
        accountId,
      });

      setTableData((prev) => [...prev, res.data]);
    } catch (_) {
      toast("An unexpected error occured when adding data");
    }
  };

  const onDeleteRows = async () => {
    try {
      const promises = [];

      setTableData(
        tableData.filter((row) => !selectedRows.find((v) => v.id === row.id))
      );

      setRowSelection({});

      promises.push(
        selectedRows.map(async (row) => {
          if (!!row?.id) {
            return deleteRevenueForecast.mutateAsync({
              id: row.id,
              accountId,
            });
          }
        })
      );
      await Promise.all(promises);
    } catch (_) {
      toast("An unexpected error occured when deleting rows");
    }
  };

  const onChangeData = useCallback(
    async (data: Partial<APRevenueForecast>, id: number) => {
      try {
        setTableData((prev) =>
          prev.map((v) => (v.id === id ? { ...v, ...data } : v))
        );

        await updateRevenueForecast.mutateAsync({
          accountId,
          id,
          data,
        });
      } catch (_) {
        toast("An unexpected error occured when modifying data");
      }
    },
    [accountId, updateRevenueForecast]
  );

  const columns: ColumnDef<APRevenueForecast>[] = useMemo(
    () => [
      {
        accessorKey: "timespan",
        header: "Future",
        size: 190,
        cell: ({ row, table }) => {
          const isPreview =
            (table.options.meta as DataTableMeta)?.isPreview ?? false;
          const rowData = row.original;

          return (
            <SelectCell
              isPreview={isPreview}
              value={rowData.timespan}
              onChange={(timespan) => {
                onChangeData({ timespan }, rowData.id);
              }}
              options={[3, 6, 9, 12, 18, 24, 36].map((v) => ({
                label: `${v} months`,
                value: v.toString(),
              }))}
              className={isPreview ? "text-4xl" : "text-[16px]"}
            />
          );
        },
        meta: {
          tooltip:
            "Specifies the time frame being analyzed for revenue data, such as the past 6, 12, or 24 months.",
          padding: true,
        },
      },
      {
        accessorKey: "low_scenario",
        header: "Low (Currency Value)",
        size: 270,
        cell: ({ row, table }) => {
          const isPreview =
            (table.options.meta as DataTableMeta)?.isPreview ?? false;
          const rowData = row.original;

          return (
            <EditableNumberCell
              value={rowData.low_scenario ?? 0}
              onChange={(low_scenario) => {
                onChangeData({ low_scenario }, rowData.id);
              }}
              className={isPreview ? "text-4xl" : "text-[16px]"}
            />
          );
        },
        meta: {
          tooltip:
            "Make a realistic assumption on the sustainability of the existing revenue plus an assumption of only medium-high probability win opportunities currently in play; assume some new opportunities; and partial additional resources added.",
          padding: true,
        },
      },
      {
        accessorKey: "realistic_scenario",
        header: "Realistic (Currency Value)",
        size: 270,
        cell: ({ row, table }) => {
          const isPreview =
            (table.options.meta as DataTableMeta)?.isPreview ?? false;
          const rowData = row.original;

          return (
            <EditableNumberCell
              value={rowData.realistic_scenario ?? 0}
              onChange={(realistic_scenario) => {
                onChangeData({ realistic_scenario }, rowData.id);
              }}
              className={isPreview ? "text-4xl" : "text-[16px]"}
            />
          );
        },
        meta: {
          tooltip:
            "Make a realistic assumption on the sustainability of the existing revenue plus an assumption of only medium-high probability win opportunities currently in play; assume some new opportunities; and partial additional resources added.",
          padding: true,
        },
      },
      {
        accessorKey: "high_scenario",
        header: "High (Currency Value)",
        size: 270,
        cell: ({ row, table }) => {
          const isPreview =
            (table.options.meta as DataTableMeta)?.isPreview ?? false;
          const rowData = row.original;

          return (
            <EditableNumberCell
              value={rowData.high_scenario ?? 0}
              onChange={(high_scenario) => {
                onChangeData({ high_scenario }, rowData.id);
              }}
              className={isPreview ? "text-4xl" : "text-[16px]"}
            />
          );
        },
        meta: {
          tooltip:
            "Make a realistic assumption on the sustainability of the existing revenue plus an assumption of only medium-high probability win opportunities currently in play; assume some new opportunities; and partial additional resources added.",
          padding: true,
        },
      },
    ],
    [onChangeData]
  );

  return (
    <AccountTable
      type={AccountPlanTableType.REVENUE_FORECAST}
      footer={
        <>
          <Button
            onClick={onAddRow}
            isLoading={createRevenueForecast.isPending}
          >
            Add row
          </Button>
          <Button
            variant="destructive"
            disabled={selectedRows.length === 0}
            onClick={onDeleteRows}
          >
            Delete Row
          </Button>
        </>
      }
    >
      <DataTable
        columns={columns}
        data={tableData}
        rowSelection={rowSelection}
        setRowSelection={setRowSelection}
        currency={accountPlan?.account_plan_group?.currency}
        tableType={AccountPlanTableType.REVENUE_FORECAST}
      />
    </AccountTable>
  );
};
